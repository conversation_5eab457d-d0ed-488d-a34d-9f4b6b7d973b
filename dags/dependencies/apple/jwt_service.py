"""
Apple JWT Token Service

This service handles JWT token generation for Apple App Store Connect API authentication.
Tokens are valid for 20 minutes and use ES256 algorithm as required by Apple.
"""

import jwt
import time
import os
import logging
from typing import Optional
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class AppleJWTService:
    """Service for generating Apple App Store Connect API JWT tokens."""

    def __init__(self):
        """Initialize the JWT service with credentials from environment variables."""
        self.key_id = os.getenv("APPLE_KEY_ID")
        self.issuer_id = os.getenv("APPLE_ISSUER_ID")
        self.private_key_path = os.getenv("APPLE_PRIVATE_KEY_PATH")

        # Validate required environment variables
        if not all([self.key_id, self.issuer_id, self.private_key_path]):
            missing_vars = []
            if not self.key_id:
                missing_vars.append("APPLE_KEY_ID")
            if not self.issuer_id:
                missing_vars.append("APPLE_ISSUER_ID")
            if not self.private_key_path:
                missing_vars.append("APPLE_PRIVATE_KEY_PATH")

            raise ValueError(
                f"Missing required environment variables: {', '.join(missing_vars)}"
            )

    def _load_private_key(self) -> str:
        """
        Load the private key from file.

        Returns:
            str: The private key content

        Raises:
            FileNotFoundError: If the private key file doesn't exist
            IOError: If there's an error reading the file
        """
        try:
            if not os.path.exists(self.private_key_path):
                raise FileNotFoundError(
                    f"Private key file not found: {self.private_key_path}"
                )

            with open(self.private_key_path, "r") as key_file:
                private_key = key_file.read()

            if not private_key.strip():
                raise ValueError("Private key file is empty")

            logger.info(f"Successfully loaded private key from {self.private_key_path}")
            return private_key

        except Exception as e:
            logger.error(f"Error loading private key: {e}")
            raise

    def generate_jwt_token(self) -> Optional[str]:
        """
        Generate a JWT token for Apple App Store Connect API.

        The token is valid for 20 minutes (maximum allowed by Apple) and uses
        the ES256 algorithm as required by Apple's API.

        Returns:
            Optional[str]: JWT token string if successful, None if failed

        Raises:
            Exception: If token generation fails
        """
        try:
            # Load the private key
            private_key = self._load_private_key()

            # JWT expiration time (20 minutes from now - maximum allowed by Apple)
            expiration_time = int(time.time()) + (20 * 60)

            # JWT payload
            payload = {
                "iss": self.issuer_id,  # Issuer ID
                "exp": expiration_time,  # Expiration time
                "aud": "appstoreconnect-v1",  # Audience (fixed for App Store Connect)
            }

            # Generate JWT token
            token = jwt.encode(
                payload,
                private_key,
                algorithm="ES256",  # Apple requires ES256 algorithm
                headers={"kid": self.key_id},  # Key ID in header
            )

            expiry_datetime = datetime.fromtimestamp(expiration_time)
            logger.info(
                f"Successfully generated JWT token. Expires at: {expiry_datetime}"
            )

            return token

        except Exception as e:
            logger.error(f"Error generating JWT token: {e}")
            raise

    def is_token_valid(self, token: str) -> bool:
        """
        Check if a JWT token is still valid (not expired).

        Args:
            token (str): The JWT token to validate

        Returns:
            bool: True if token is valid, False otherwise
        """
        try:
            # Decode without verification to check expiration
            decoded = jwt.decode(token, options={"verify_signature": False})
            exp_timestamp = decoded.get("exp")

            if not exp_timestamp:
                return False

            # Check if token is expired (with 1 minute buffer)
            current_time = int(time.time())
            return exp_timestamp > (current_time + 60)

        except Exception as e:
            logger.error(f"Error validating token: {e}")
            return False

    def get_token_expiry(self, token: str) -> Optional[datetime]:
        """
        Get the expiration datetime of a JWT token.

        Args:
            token (str): The JWT token

        Returns:
            Optional[datetime]: Expiration datetime if successful, None otherwise
        """
        try:
            decoded = jwt.decode(token, options={"verify_signature": False})
            exp_timestamp = decoded.get("exp")

            if exp_timestamp:
                return datetime.fromtimestamp(exp_timestamp)
            return None

        except Exception as e:
            logger.error(f"Error getting token expiry: {e}")
            return None
