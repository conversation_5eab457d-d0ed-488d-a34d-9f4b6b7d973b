"""
Apple Analytics Configuration

This module defines configuration constants for Apple Analytics reports,
including report targeting, data processing settings, and Supabase integration.
"""

from enum import Enum
from typing import Dict, List, Optional
from dataclasses import dataclass


class ReportCategory(Enum):
    """Categories of Apple Analytics reports."""
    APP_USAGE = "APP_USAGE"
    COMMERCE = "COMMERCE"
    FRAMEWORK_USAGE = "FRAMEWORK_USAGE"
    PERFORMANCE = "PERFORMANCE"


@dataclass
class ReportConfig:
    """Configuration for a specific Apple Analytics report."""
    name: str
    category: ReportCategory
    priority: int  # 1 = highest priority
    enabled: bool = True
    description: Optional[str] = None


# Target reports configuration - focused on active users, downloads, and sessions
TARGET_REPORTS = {
    # Core reports for the metrics we need
    "app_downloads_standard": ReportConfig(
        name="App Downloads Standard", 
        category=ReportCategory.COMMERCE,
        priority=1,
        description="Total download counts and download types"
    ),
    "app_store_installation_deletion": ReportConfig(
        name="App Store Installation and Deletion Detailed",
        category=ReportCategory.COMMERCE,
        priority=1,
        description="Detailed install/uninstall events (for active user calculation)"
    ),
    "app_sessions_standard": ReportConfig(
        name="App Sessions Standard",
        category=ReportCategory.APP_USAGE,
        priority=1,
        description="Daily active users, sessions, and engagement time"
    ),
    "app_sessions_detailed": ReportConfig(
        name="App Sessions Detailed", 
        category=ReportCategory.APP_USAGE,
        priority=2,
        description="Detailed session data with granular breakdowns"
    ),
}

# Apple API Configuration
APPLE_API_CONFIG = {
    "base_url": "https://api.appstoreconnect.apple.com/v1",
    "timeout_seconds": 30,
    "max_retries": 3,
    "retry_delay_seconds": 5,
    "rate_limit_requests_per_second": 5,  # Apple's rate limit
    "jwt_expiry_minutes": 20,  # Maximum allowed by Apple
}

# Report processing configuration
REPORT_PROCESSING_CONFIG = {
    "access_type": "ONGOING",  # Use existing ongoing report request
    "existing_report_request_id": "3bfe16a0-ded2-444c-82fb-ec819d301f01",
    "max_poll_minutes": 30,
    "poll_interval_seconds": 60,
    "download_timeout_seconds": 300,
    "max_file_size_mb": 100,
}

# Supabase configuration
SUPABASE_CONFIG = {
    "table_name": "apple_analytics_data",
    "batch_size": 100,
    "upsert_conflict_column": "unique_id",  # Primary key for deduplication
    "max_retries": 3,
    "retry_delay_seconds": 2,
    "summary_tables": {
        "daily_summary": "apple_analytics_daily_summary",
        "app_summary": "apple_analytics_app_summary",
    },
}

# Data transformation configuration
DATA_TRANSFORM_CONFIG = {
    "date_format": "%Y-%m-%d",
    "timezone": "UTC",
    "exclude_test_data": True,
    "min_device_count": 1,  # Filter out rows with very low device counts
    "required_columns": [
        "Date", "App Name", "App Apple Identifier", "Event", 
        "Device", "Platform Version", "Territory", "Counts", "Unique Devices"
    ],
}

# Secret Manager configuration
SECRET_MANAGER_CONFIG = {
    "project_id": "phia-prod-416420",
    "secrets": {
        "apple_key_id": "apple-key-id",
        "apple_issuer_id": "apple-issuer-id", 
        "apple_private_key_path": "apple-private-key-path",
        "apple_app_id": "apple-app-id",
        "supabase_url": "supabase-url",
        "supabase_service_role_key": "supabase-service-role-key",
    }
}

# DAG configuration
DAG_CONFIG = {
    "dag_id": "apple_analytics_reports",
    "schedule_interval": "0 4 * * *",  # Daily at 4 AM UTC
    "start_date": "2024-01-01",
    "max_active_runs": 1,
    "catchup": False,
    "retries": 2,
    "retry_delay_minutes": 10,
    "email_on_failure": True,
    "email_on_retry": False,
    "owner": "data-team",
    "tags": ["apple", "analytics", "mobile"],
    "depends_on_past": False,
}

# Logging configuration
LOGGING_CONFIG = {
    "log_level": "INFO",
    "enable_debug": False,
    "log_api_requests": True,
    "log_response_bodies": False,  # Set to True for debugging
    "performance_logging": True,
}


def get_enabled_reports() -> List[ReportConfig]:
    """Get list of enabled reports sorted by priority."""
    enabled = [config for config in TARGET_REPORTS.values() if config.enabled]
    return sorted(enabled, key=lambda x: x.priority)


def get_report_config(report_name: str) -> Optional[ReportConfig]:
    """Get configuration for a specific report by name."""
    for config in TARGET_REPORTS.values():
        if config.name == report_name:
            return config
    return None


def get_reports_by_category(category: ReportCategory) -> List[ReportConfig]:
    """Get all enabled reports for a specific category."""
    return [
        config for config in TARGET_REPORTS.values() 
        if config.category == category and config.enabled
    ]